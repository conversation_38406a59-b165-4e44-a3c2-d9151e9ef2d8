import { useTranslations } from 'next-intl'
import { TRACK_EVENT, useVolcAnalytics } from '@ninebot/core'
import { AutoComplete } from 'antd'
import type { RefSelectProps } from 'antd/es/select'

import { Clear, Search } from '@/components'

interface SearchInputProps {
  searchValue: string
  options?: { value: string; label: JSX.Element }[]
  onSearch: (value: string) => void
  onChange: (value: string) => void
  onClose: () => void
  onSubmit: (value: string) => void
  inputRef?: React.RefObject<RefSelectProps>
  placeholder?: string
  onSearchClick?: () => void
}

export default function SearchInput({
  searchValue,
  options = [],
  onSearch,
  onChange,
  onClose,
  onSubmit,
  inputRef,
  placeholder = '',
  onSearchClick,
}: SearchInputProps) {
  const getI18nString = useTranslations('Common')
  const { reportEvent } = useVolcAnalytics()

  const handleSelect = (value: string) => {
    onSubmit(value)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLElement>) => {
    if (e.key === 'Enter' && (searchValue.trim() || placeholder)) {
      onSubmit(searchValue || placeholder)
    }
  }

  const handleSearchClick = () => {
    // 调用外部传入的搜索点击处理函数
    onSearchClick?.()

    if (searchValue.trim() || placeholder) {
      // 埋点：搜索结果页搜索框点击
      reportEvent(TRACK_EVENT.shop_searchresult_search_click, {
        search_word: searchValue || placeholder,
      })

      onSubmit(searchValue || placeholder)
    }
  }

  // 取消搜索
  const handleCancel = () => {
    // 埋点：搜索页取消按钮点击（根据当前页面判断）
    if (window.location.pathname.includes('/catalogsearch/result')) {
      reportEvent(TRACK_EVENT.shop_searchresult_cancel_button_click, {
        button_id: 'shop_seach_cancel',
      })
    } else {
      reportEvent(TRACK_EVENT.shop_searchpage_cancel_button_click, {
        button_id: 'shop_seach_cancel',
      })
    }

    onClose()
  }

  return (
    <div className="flex items-center justify-center">
      <AutoComplete
        className="search-input"
        ref={inputRef}
        value={searchValue}
        options={options}
        onSearch={onSearch}
        onChange={(value) => onChange(value)}
        onSelect={handleSelect}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        size="large"
        allowClear={{ clearIcon: <Clear bg="white" /> }}
        variant="borderless"
        prefix={
          <button
            onClick={handleSearchClick}
            className="mr-[8px] flex items-center justify-center hover:text-primary"
            type="button">
            <Search isSmall size={24} />
          </button>
        }
      />
      <button
        onClick={handleCancel}
        className="ml-[16px] whitespace-nowrap font-miSansMedium380 text-[16px] leading-[140%] text-[#6E6E73]">
        {getI18nString('cancel')}
      </button>
    </div>
  )
}
