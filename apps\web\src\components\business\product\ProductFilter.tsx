'use client'

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { mergeStyles, TRACK_EVENT, useAuth, useVolcAnalytics } from '@ninebot/core'
import { Checkbox } from 'antd'

import { IconArrow } from '@/components'

import styles from './styles'

type SortOption = {
  label: string
  value: 'ASC' | 'DESC'
  key: string
  sort: number
}

type NewSortOption = {
  label: string
  value: number
  key: string
}

const sortOptions: SortOption[] = [
  { label: '综合', value: 'ASC', key: 'position', sort: 1 },
  // { label: 'New Arrivals', value: 'new' },
  { label: '价格从高到低', value: 'DESC', key: 'price', sort: 2 },
  { label: '价格从低到高', value: 'ASC', key: 'price', sort: 3 },
  // { label: '已补货', value: 'in-stock' },
  // { label: '其他文案', value: 'other' },
]

interface ProductFilterProps {
  total?: number
  hasCategories?: boolean
  sort?: {
    [key: string]: 'ASC' | 'DESC'
  }
  setSort?: React.Dispatch<
    React.SetStateAction<{
      [key: string]: 'ASC' | 'DESC'
    }>
  >
  isDigital: boolean
  onlyMyCar: boolean
  setOnlyMyCar: (onlyMyCar: boolean) => void
}

const ProductFilter = ({
  total = 0,
  hasCategories = false,
  sort,
  setSort,
  isDigital,
  onlyMyCar,
  setOnlyMyCar,
}: ProductFilterProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const getI18nString = useTranslations('Web')
  const getI18nStringCommon = useTranslations('Common')
  const { isLoggedIn } = useAuth()
  const { reportEvent } = useVolcAnalytics()

  const newSortOptions: NewSortOption[] = [
    {
      label: getI18nString('position'),
      value: 1,
      key: 'position',
    },
    {
      label: getI18nString('price'),
      value: 2,
      key: 'price',
    },
  ]

  const initialSortKey = useMemo(() => {
    if (sort) {
      return Object.keys(sort)[0]
    }

    return 'position'
  }, [sort])
  const [sortType, setSortType] = useState(sort?.[initialSortKey] || 'ASC')
  const [activeOption, setActiveOption] = useState(initialSortKey)

  const handleOptionPress = useCallback(
    (option: NewSortOption) => {
      if (initialSortKey === 'position' && option.key === 'position') {
        return
      }

      setActiveOption(option.key)

      let currentSortType = sortType
      if (option.key === 'price') {
        currentSortType = sortType === 'DESC' ? 'ASC' : 'DESC'
        setSortType(currentSortType)
      } else {
        setSortType('ASC')
        currentSortType = 'ASC'
      }

      setSort?.({
        [option.key]: currentSortType,
      })

      reportEvent(TRACK_EVENT.shop_sort_option_click, {
        sort_type: option.key,
        sort_order: currentSortType,
        sort_name: option.label,
      })
    },
    [initialSortKey, setSort, sortType, reportEvent],
  )

  useEffect(() => {
    if (initialSortKey !== activeOption) {
      setSortType(sort?.[initialSortKey] || 'ASC')
      setActiveOption(initialSortKey)
    }
  }, [initialSortKey, activeOption, sort])

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  if (hasCategories) {
    return (
      <div className="mb-8 flex items-center justify-between">
        <div className="font-miSansMedium380 text-[14px] leading-[140%] text-[#444446]">
          {getI18nString('total_count', { key: total })}
        </div>
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => {
              reportEvent(TRACK_EVENT.shop_sort_tab_click, {
                sort_type: activeOption,
                sort_order: sortType,
              })
              setIsOpen(!isOpen)
            }}
            className="flex items-center gap-[12px]">
            <span
              className={mergeStyles([
                'text-right font-miSansDemiBold450 text-[16px] leading-[140%] text-[#000000]',
                isOpen && 'text-primary',
              ])}>
              {getI18nString('sort_by')}
              {
                sortOptions.find((item) => item.key === activeOption && item.value === sortType)
                  ?.label
              }
            </span>
            <IconArrow color={isOpen ? '#DA291C' : '#000000'} rotate={isOpen ? 180 : 0} />
          </button>

          {isOpen && (
            <div
              className="absolute right-0 top-full z-10 mt-8 w-[220px] rounded-[12px] border border-gray-100 bg-[#FFFFFFD9] px-[16px] py-[12px] shadow-lg"
              style={{
                backdropFilter: 'blur(20px)',
                WebkitBackdropFilter: 'blur(20px)', // Safari 兼容
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
              }}>
              {sortOptions.map((option, index) => (
                <button
                  key={option.sort}
                  onClick={() => {
                    if (activeOption === option.key && sortType === option.value) return

                    // 埋点：排序条件点击
                    reportEvent(TRACK_EVENT.shop_sort_option_click, {
                      sort_type: option.key,
                      sort_order: option.value,
                      sort_name: option.label,
                    })

                    setIsOpen(false)
                    setSort?.({
                      [option.key]: option.value,
                    })
                  }}
                  className={mergeStyles(
                    'w-full py-[16px] text-left font-miSansMedium380 text-[16px] leading-[140%] hover:text-[#DA291C]',
                    activeOption === option.key && sortType === option.value
                      ? 'text-primary'
                      : 'text-[#000000]',
                    index < sortOptions.length - 1 ? 'border-b-[0.5px] border-b-[#00000014]' : '',
                  )}>
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-[32px] flex items-center justify-between">
      <div className="flex gap-[16px]">
        {newSortOptions.map((option) => (
          <button
            key={option.key}
            className={
              activeOption === option.key
                ? mergeStyles([styles.option, styles.active])
                : styles.option
            }
            onClick={() => handleOptionPress(option)}>
            <span
              className={
                activeOption === option.key
                  ? mergeStyles([styles.optionText, styles.textActive])
                  : styles.optionText
              }>
              {option.label}
            </span>
            {['price'].includes(option.key) && (
              <div className={styles.sortIcon}>
                <div
                  className={mergeStyles([
                    styles.arrowTop,
                    sort?.[option.key] === 'ASC' && 'border-b-[#DA291C]',
                  ])}
                />
                <div
                  className={mergeStyles([
                    styles.arrowBottom,
                    sort?.[option.key] === 'DESC' && 'border-t-[#DA291C]',
                  ])}
                />
              </div>
            )}
          </button>
        ))}
      </div>
      {isDigital && isLoggedIn && (
        <Checkbox checked={onlyMyCar} onChange={(e) => setOnlyMyCar(e.target.checked)}>
          {getI18nStringCommon('only_my_car')}
        </Checkbox>
      )}
    </div>
  )
}

export default ProductFilter
