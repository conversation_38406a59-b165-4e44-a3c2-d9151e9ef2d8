'use client'
import { useMemo } from 'react'
import {
  decodeBase64,
  IconPlus,
  NCoinView,
  Price,
  resolveCatchMessage,
  ROUTE,
  setCancelOrderId,
  sleep,
  useCancelOrderMutation,
  useCancelOrderReturnMutation,
  useDebounceFn,
  useNavigate,
  useUserOrder,
} from '@ninebot/core'
import { useAppDispatch } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd'

import { Confirm } from '@/businessComponents/Confirm'

import { useOrderDetail } from '../../context/orderDetailContext'

export default function ActionButtons() {
  const {
    orderData: order,
    hasNCoin,
    isOnlyNCoin,
    toast,
    loading,
    currentTime,
    refetch: refetchOrderDetail,
    handleReturnOrder,
    getI18nString,
  } = useOrderDetail()

  const dispatch = useAppDispatch()

  const { checkOrderPayInfo } = useUserOrder()
  const { openPage } = useNavigate()
  const [cancelOrder] = useCancelOrderMutation()
  const [cancelOrderReturn] = useCancelOrderReturnMutation()
  /**
   * 是否待付款
   */
  const isWaitPay = useMemo(
    () => order?.status_code === 'pending' && Number(order?.payment_time_out) > currentTime / 1000,
    [order?.status_code, order?.payment_time_out, currentTime],
  )

  /**
   * 是否已发货
   */
  const isDelivered = useMemo(
    () => order?.status_code === 'shipped' || order?.status_code === 'delivered',
    [order?.status_code],
  )

  /**
   * 是否可取消申请
   */
  const isCanCancel = useMemo(
    () => order?.can_cancel_requisition === true,
    [order?.can_cancel_requisition],
  )

  /**
   * 是否可退货退款
   */
  const isReturnAble = useMemo(() => order?.can_requisition === true, [order?.can_requisition])

  /**
   * 跳转支付
   */
  const { run: handleRepay } = useDebounceFn(async () => {
    if (order?.encrypt?.nid) {
      const result = await checkOrderPayInfo(order.encrypt.nid)
      // 202 跳转支付
      if (result?.code === 202) {
        openPage({
          route: ROUTE.checkoutPaying,
          queryParams: {
            orderId: order?.encrypt?.nid,
          },
          replace: true,
        })
        return
      }
      // 200 已支付
      if (result?.code === 200) {
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: getI18nString('order_has_paid'),
        })
        await sleep(500)
        refetchOrderDetail()
        return
      }
      // 支付失败
      await sleep(500)
      toast.show({
        icon: 'fail',
        content: result?.message || getI18nString('fetch_data_error'),
      })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 取消订单
   */
  const { run: confirmCancelOrder } = useDebounceFn(() => {
    if (order?.id) {
      loading.show()
      cancelOrder({
        input: {
          reason: getI18nString('proactively_cancel_the_order'),
          order_id: decodeBase64(order.id),
        },
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (res?.cancelOrder?.order?.id) {
            toast.show({
              icon: 'success',
              content: getI18nString('order_has_canceled'),
            })
            await sleep(500)
            // 更新订单详情页
            refetchOrderDetail()
            // 更新订单列表页
            dispatch(setCancelOrderId(order.number))
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  /**
   * 取消订单确认弹窗
   */
  const handleCancelOrder = async () => {
    const confirmed = await Confirm.confirm({
      title: getI18nString('confirm_cancel_order'),
      okText: getI18nString('cancel_order'),
      cancelText: getI18nString('thinking'),
    })

    if (confirmed) {
      confirmCancelOrder()
    }
  }

  /**
   * 取消退款申请
   */
  const { run: handleCancelReturnOrder } = useDebounceFn(() => {
    if (order?.encrypt?.nid) {
      loading.show()
      cancelOrderReturn({
        orderNumber: order.encrypt.nid,
      })
        .unwrap()
        .then(async (res) => {
          loading.hide()
          await sleep(500)
          if (res?.cancelOrderRequisition?.status) {
            toast.show({
              icon: 'success',
              content: getI18nString('cancelled'),
            })
            await sleep(500)
            refetchOrderDetail()
          } else {
            toast.show({
              icon: 'fail',
              content: res?.cancelOrderRequisition?.message || getI18nString('fetch_data_error'),
            })
          }
        })
        .catch(async (error) => {
          loading.hide()
          await sleep(500)
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        })
    } else {
      toast.show({
        icon: 'fail',
        content: getI18nString('fetch_data_error'),
      })
    }
  })

  // 根据状态显示不同的按钮
  const renderActionButtons = () => {
    return (
      <>
        <div className="flex items-center gap-base-12">
          {isWaitPay && (
            <>
              <Button
                style={{ padding: '8px 24px', height: '38px', lineHeight: '20px' }}
                onClick={handleCancelOrder}>
                {getI18nString('cancel_order')}
              </Button>

              <Button
                type="primary"
                style={{ padding: '8px 24px', height: '38px', lineHeight: '20px' }}
                onClick={handleRepay}>
                <div>{getI18nString('immediate_payment')}</div>
                <div className="flex items-center">
                  {isOnlyNCoin ? (
                    <NCoinView
                      number={Number(order?.ncoin_pay?.grand_total)}
                      iconStyle={{ background: '#FFFFFF', color: '#000000' }}
                      textStyle="text-white"
                    />
                  ) : hasNCoin ? (
                    <div className="flex items-center gap-[4px]">
                      <Price
                        color="white"
                        price={order?.total?.grand_total}
                        currencyStyle="text-[16px] font-miSansSemibold520 leading-[1.2]"
                        textStyle="text-[16px] font-miSansSemibold520 leading-[1.2]"
                        fractionStyle="text-[16px] font-miSansSemibold520 leading-[1.2]"
                      />
                      <IconPlus color="#FFFFFF" />
                      <NCoinView
                        number={Number(order?.ncoin_pay?.grand_total)}
                        iconStyle={{ background: '#FFFFFF', color: '#000000' }}
                        textStyle="text-white"
                      />
                    </div>
                  ) : (
                    <Price
                      color="white"
                      price={order?.total?.grand_total}
                      currencyStyle="text-[16px] font-miSansSemibold520 leading-[1.2]"
                      textStyle="text-[16px] font-miSansSemibold520 leading-[1.2]"
                      fractionStyle="text-[16px] font-miSansSemibold520 leading-[1.2]"
                    />
                  )}
                </div>
              </Button>
            </>
          )}

          {isCanCancel ? (
            <Button
              style={{ padding: '8px 24px', height: '38px', lineHeight: '20px' }}
              onClick={handleCancelReturnOrder}>
              {getI18nString('cancel_return')}
            </Button>
          ) : (
            isReturnAble && (
              <Button
                style={{ padding: '8px 24px', height: '38px', lineHeight: '20px' }}
                onClick={handleReturnOrder}>
                {isDelivered ? getI18nString('return_and_refund') : getI18nString('refund')}
              </Button>
            )
          )}
        </div>
      </>
    )
  }

  return (isWaitPay || isReturnAble || isCanCancel) && renderActionButtons()
}
